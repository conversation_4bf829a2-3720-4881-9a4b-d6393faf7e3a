<template>
  <div class="gzhyx">
    <van-form scroll-to-error>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/mode.png" alt="" />
            <span class="word">选择产品</span>
          </template>
        </van-cell>
        <domestic-product
          :subjectId.sync="form.subject_id"
          :subjectCategoryIds.sync="form.subject_category_ids"
          :manualConditions.sync="form.manual_conditions"
          :productId.sync="form.product_id"
          :is-new.sync="form.is_new"
          :is-except-goods.sync="isExceptGoods"
          @change="(v) => (product = v)"
        />
      </div>
      <div ref="showInput">
        <!-- 投保人/被保人信息 -->
        <div class="item-wrapper">
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/insure/mode.png" alt="" />
              <span class="word">投保人/被保人信息</span>
            </template>
          </van-cell>
          <!-- 选择投保人 -->
          <van-field
            v-model="form.policyholder"
            label="投保人"
            placeholder="请输入投保人姓名"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <template>
            <!-- 投保人客户类型 -->
            <van-field name="radio" label="投保人客户类型">
              <template #input>
                <van-radio-group v-model="form.policyholder_type" direction="horizontal">
                  <van-radio :name="0">团体客户</van-radio>
                  <van-radio :name="1">个人客户</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </template>
          <div>
            <van-field name="radio" label="投保人是否为境外客户" v-if="product?.company?.identifier === 'PICC'">
              <template #input>
                <van-radio-group v-model="form.policyholder_overseas" direction="horizontal">
                  <van-radio :name="0">否</van-radio>
                  <van-radio :name="1">是</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.policyholder_idcard_no"
              :label="form.policyholder_type === 1 ? '投保人证件号' : '投保人统一社会信用代码'"
              :placeholder="form.policyholder_type === 1 ? '请输入投保人证件号' : '请输入投保人统一社会信用代码'"
              :class="
                ['HUATAI', 'TPIC', 'DIC', 'PINGAN'].includes(product?.company?.identifier) ||
                (product?.company?.identifier == 'PICC' && form.policyholder_type == 1)
                  ? 'required'
                  : ''
              "
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <div
              v-if="
                ['DIC'].includes(product?.company?.identifier) ||
                (form.policyholder_type === 1 && product?.company?.identifier === 'PICC')
              "
            >
              <van-field
                readonly
                clickable
                :value="form.policyholder_idcard_issue_date"
                label="投保人证件有效起始时间"
                placeholder="点击选择投保人证件有效起始时间"
                @click="policyholderIssueDatePicker = true"
                :class="
                  ['DIC'].includes(product?.company?.identifier) || form.policyholder_overseas === 0 ? 'required' : ''
                "
                :rules="[
                  {
                    required: ['DIC'].includes(product?.company?.identifier) || form.policyholder_overseas === 0,
                    message: '此项为必填项'
                  }
                ]"
              />
              <van-calendar
                v-model="policyholderIssueDatePicker"
                color="rgb(255, 127, 76)"
                :min-date="clientMinDate"
                :max-date="clientMaxDate"
                @confirm="confirmPolicyholderIssueDate"
              />
              <van-field
                readonly
                clickable
                :value="form.policyholder_idcard_valid_till"
                label="投保人证件有效结束时间"
                placeholder="点击选择投保人证件有效结束时间"
                @click="policyholderValidTillPicker = true"
                :class="
                  ['DIC'].includes(product?.company?.identifier) || form.policyholder_overseas === 0 ? 'required' : ''
                "
                :rules="[
                  {
                    required: ['DIC'].includes(product?.company?.identifier) || form.policyholder_overseas === 0,
                    message: '此项为必填项'
                  }
                ]"
              />
              <van-calendar
                v-model="policyholderValidTillPicker"
                color="rgb(255, 127, 76)"
                :min-date="clientMinDate"
                :max-date="clientMaxDate"
                @confirm="confirmPolicyholderValidTill"
              />
            </div>
          </div>

          <!-- 选择被保人 -->
          <van-field
            v-model="form.insured"
            label="被保人"
            placeholder="请输入被保人姓名"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          >
            <template #button>
              <van-checkbox v-model="isSame">同投保人</van-checkbox>
            </template>
          </van-field>
          <template>
            <!-- 被保人客户类型 -->
            <van-field name="radio" label="被保人客户类型">
              <template #input>
                <van-radio-group v-model="form.insured_type" direction="horizontal">
                  <van-radio :name="0">团体客户</van-radio>
                  <van-radio :name="1">个人客户</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </template>
          <div>
            <van-field name="radio" label="被保人是否为境外客户" v-if="product?.company?.identifier === 'PICC'">
              <template #input>
                <van-radio-group v-model="form.insured_overseas" direction="horizontal">
                  <van-radio :name="0">否</van-radio>
                  <van-radio :name="1">是</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-model="form.insured_idcard_no"
              :label="form.insured_type === 1 ? '被保人证件号' : '被保人统一社会信用代码'"
              :placeholder="form.insured_type === 1 ? '请输入被保人证件号' : '请输入被保人统一社会信用代码'"
              :class="
                ['HUATAI', 'TPIC', 'DIC', 'PINGAN'].includes(product?.company?.identifier) ||
                (product?.company?.identifier == 'PICC' && form.insured_type == 1)
                  ? 'required'
                  : ''
              "
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <div
              v-if="
                ['DIC'].includes(product?.company?.identifier) ||
                (form.insured_type === 1 && product?.company?.identifier === 'PICC')
              "
            >
              <van-field
                readonly
                clickable
                :value="form.insured_idcard_issue_date"
                label="被保人证件有效起始时间"
                placeholder="点击选择被保人证件有效起始时间"
                @click="insuredIssueDatePicker = true"
                :class="['DIC'].includes(product?.company?.identifier) || form.insured_overseas === 0 ? 'required' : ''"
                :rules="[
                  {
                    required: ['DIC'].includes(product?.company?.identifier) || form.insured_overseas === 0,
                    message: '此项为必填项'
                  }
                ]"
              />
              <van-calendar
                v-model="insuredIssueDatePicker"
                color="rgb(255, 127, 76)"
                :min-date="clientMinDate"
                :max-date="clientMaxDate"
                @confirm="confirmInsuredIssueDate"
              />
              <van-field
                readonly
                clickable
                :value="form.insured_idcard_valid_till"
                label="被保人证件有效结束时间"
                placeholder="点击选择被保人证件有效结束时间"
                @click="insuredValidTillPicker = true"
                :class="['DIC'].includes(product?.company?.identifier) || form.insured_overseas === 0 ? 'required' : ''"
                :rules="[
                  {
                    required: ['DIC'].includes(product?.company?.identifier) || form.insured_overseas === 0,
                    message: '此项为必填项'
                  }
                ]"
              />
              <van-calendar
                v-model="insuredValidTillPicker"
                color="rgb(255, 127, 76)"
                :min-date="clientMinDate"
                :max-date="clientMaxDate"
                @confirm="confirmInsuredValidTill"
              />
            </div>
          </div>

          <van-collapse accordion v-model="contactCollapse">
            <van-collapse-item title="更多信息" name="1">
              <!-- 投保人电话 -->
              <van-field
                v-model="form.policyholder_phone_number"
                label="投保人电话"
                placeholder="请输入投保人电话"
                :class="['DIC'].includes(product?.company?.identifier) ? 'required' : 'none-r'"
                :rules="[{ required: ['DIC'].includes(product?.company?.identifier), message: '此项为必填项' }]"
              />
              <!-- 被保人电话 -->
              <van-field
                v-model="form.insured_phone_number"
                label="被保人电话"
                placeholder="请输入被保人电话"
                :class="['DIC'].includes(product?.company?.identifier) ? 'required' : 'none-r'"
                :rules="[{ required: ['DIC'].includes(product?.company?.identifier), message: '此项为必填项' }]"
              />
              <!-- 投保人地址 -->
              <van-field
                v-model="form.policyholder_address"
                label="投保人地址"
                placeholder="请输入投保人地址"
                :class="['DIC'].includes(product?.company?.identifier) ? 'required' : 'none-r'"
                :rules="[{ required: ['DIC'].includes(product?.company?.identifier), message: '此项为必填项' }]"
              />
              <!-- 被保人地址 -->
              <van-field
                v-model="form.insured_address"
                label="被保人地址"
                placeholder="请输入被保人地址"
                :class="['DIC'].includes(product?.company?.identifier) ? 'required' : 'none-r'"
                :rules="[{ required: ['DIC'].includes(product?.company?.identifier), message: '此项为必填项' }]"
              />
            </van-collapse-item>
          </van-collapse>
        </div>
        <!-- 货物信息 -->
        <div class="item-wrapper">
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/insure/mode.png" alt="" />
              <span class="word">货物信息</span>
            </template>
          </van-cell>
          <!-- 货物类别 -->
          <van-field
            readonly
            clickable
            name="form.goods_type_id"
            :value="goodsType"
            label="货物类别"
            right-icon="arrow"
            placeholder="点击选择货物类别"
            class="required"
            @click="goodsTypePicker = true"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <van-popup v-model="goodsTypePicker" position="bottom">
            <van-picker
              show-toolbar
              :columns="goodsTypes"
              @confirm="confirmGoodsType"
              @cancel="goodsTypePicker = false"
              :default-index.sync="goodsTypeDefaultIndex"
            />
          </van-popup>
          <!-- 包装方式 -->
          <van-field
            readonly
            clickable
            name="form.packing_method_id"
            :value="packingMethod"
            label="包装方式"
            right-icon="arrow"
            placeholder="点击选择包装方式"
            class="required"
            @click="packingMethodPicker = true"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <van-popup v-model="packingMethodPicker" position="bottom">
            <van-picker
              show-toolbar
              :columns="packingMethods"
              @confirm="confirmPickingMethod"
              @cancel="packingMethodPicker = false"
              :default-index="packingMethodDefaultIndex"
            />
          </van-popup>
          <!-- 货物名称 -->
          <van-field
            v-model="form.goods_name"
            rows="2"
            autosize
            type="textarea"
            label="货物名称"
            placeholder="请输入货物名称"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <!-- 数量规格 -->
          <van-field
            v-model="form.goods_amount"
            rows="2"
            autosize
            :type="product?.company?.identifier === 'TPIC' ? 'number' : 'textarea'"
            label="数量规格"
            placeholder="请输入数量规格"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
        </div>
        <!-- 运输方式 -->
        <div class="item-wrapper">
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/insure/mode.png" alt="" />
              <span class="word">运输方式</span>
            </template>
          </van-cell>
          <!-- 运输方式 -->
          <van-field
            readonly
            clickable
            :value="transportMethod"
            label="运输方式"
            right-icon="arrow"
            placeholder="点击选择运输方式"
            @click="transportMethodPicker = true"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <van-popup v-model="transportMethodPicker" position="bottom">
            <van-picker
              show-toolbar
              :columns="transportMethods"
              @confirm="confirmTransportMethod"
              @cancel="transportMethodPicker = false"
              :default-index="transportMethodDefaultIndex"
            />
          </van-popup>
          <!-- 装载方式 -->
          <van-field
            readonly
            clickable
            :value="loadingMethod"
            label="装载方式"
            right-icon="arrow"
            placeholder="点击选择装载方式"
            @click="loadingMethodPicker = true"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <van-popup v-model="loadingMethodPicker" position="bottom">
            <van-picker
              show-toolbar
              :columns="loadingMethods"
              @confirm="confirmLoadingMethod"
              @cancel="loadingMethodPicker = false"
              :default-index="loadingMethodDefaultIndex"
            />
          </van-popup>
          <!-- 分割 ---------------------- -->
          <van-field
            readonly
            clickable
            label="起运地"
            :value="form.departure"
            placeholder="点击选择省市区"
            @click="showDeparture = true"
            right-icon="arrow"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          >
          </van-field>
          <tpic-ports
            v-if="product?.company?.identifier === 'TPIC'"
            v-model="form.departure_port"
            :suggestion="form.departure"
            label="起运地"
          />
          <van-field name="area" label=" " v-model="form.departure_addr" placeholder="起运地保单地址" />

          <!-- 中转地 -->
          <van-field
            readonly
            clickable
            label="中转地"
            :value="form.transmit"
            placeholder="点击选择省市区"
            @click="showTransmit = true"
            right-icon="arrow"
          >
          </van-field>
          <tpic-ports
            v-if="product?.company?.identifier === 'TPIC'"
            :is-required="false"
            v-model="form.transmit_port"
            :suggestion="form.transmit"
            label="中转地"
          />
          <van-field label=" " v-model="form.transmit_addr" placeholder="中转地保单地址" />

          <van-field
            readonly
            clickable
            label="目的地"
            :value="form.destination"
            placeholder="点击选择省市区"
            @click="showDestination = true"
            right-icon="arrow"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          >
          </van-field>

          <tpic-ports
            v-if="product?.company?.identifier === 'TPIC'"
            v-model="form.destination_port"
            :suggestion="form.destination"
            label="目的地"
          />
          <van-field label=" " v-model="form.destination_addr" placeholder="目的地保单地址" />
          <!-- 分割 ---------------------- -->
          <!-- 发票号（港口）-->
          <van-field
            v-model="form.invoice_no"
            label="发票号"
            :placeholder="product?.company?.identifier === 'PICC' ? '发票号' : '运单号和发票号必填一个'"
          />
          <van-field
            v-model="form.waybill_no"
            label="运单号"
            :placeholder="product?.company?.identifier === 'PICC' ? '运单号' : '运单号和发票号必填一个'"
          />
          <!-- 船名航次/航班号/车牌号 -->
          <van-field
            v-model="form.transport_no"
            label="运输工具号"
            placeholder="运输工具号"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <!-- 船舶建造年-->
          <van-field
            v-if="product?.company?.identifier === 'PICC' && form?.transport_method_id == 2"
            v-model="form.ship_construction_year"
            is-link
            readonly
            label="船舶建造年"
            placeholder="请选择船舶建造年"
            @click="shipConstructionYearPicker.show = true"
          />
          <van-popup v-model="shipConstructionYearPicker.show" round position="bottom">
            <van-datetime-picker
              v-model="shipConstructionYearPicker.value"
              type="year-month"
              title="选择船舶建造年"
              @cancel="shipConstructionYearPicker.show = false"
              @confirm="confirmShipConstructionYear"
            />
          </van-popup>
          <!-- 起运日期-->
          <van-field
            readonly
            clickable
            :value="form.shipping_date"
            label="起运日期"
            placeholder="点击选择日期"
            @click="shippingDatePicker = true"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
          />
          <van-calendar
            v-model="shippingDatePicker"
            color="rgb(255, 127, 76)"
            :min-date="shippingDatePickerMinDate"
            @confirm="confirmShippingDate"
          />
          <!--        倒签保函-->
          <van-field v-if="showAntiDatedFileUploader" name="倒签保函">
            <template #label>
              <div>倒签保函</div>
              <div style="`font-size: 10px; color: ${primaryColor}; white-space: nowrap`">下载倒签保函</div>
            </template>
            <template #input>
              <van-uploader :after-read="uploadAntiDatedFile" :max-count="1" :max-size="2 * 1024 * 1024">
                <span style="margin-right: 20px">{{ fileName }}</span>
                <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
              </van-uploader>
            </template>
          </van-field>
        </div>
        <!-- 费用信息-->
        <div class="item-wrapper">
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/insure/mode.png" alt="" />
              <span class="word">费用信息</span>
            </template>
          </van-cell>
          <!-- 保费金额 -->
          <van-field
            v-model="form.coverage"
            label="保险金额（元）"
            type="number"
            class="required"
            :rules="[{ required: true, message: '此项为必填项' }]"
            @focus.once="
              () =>
                $route.query.from === 'edit'
                  ? $dialog.alert({ title: '提醒', message: '修改保险金额需要上传盖章发票', theme: 'round-button' })
                  : ''
            "
            placeholder="请输入保险金额（元）"
          />
          <van-field v-if="form.coverage > 0" readonly label=" " :value="form.coverage | chineseCoverage" />
        </div>
        <!-- 其他信息-->
        <div class="item-wrapper none-r">
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/insure/mode.png" alt="" />
              <span class="word">其他信息</span>
            </template>
          </van-cell>
          <van-field v-model="form.sticky_note" rows="2" label="工作编号" placeholder="请输入工作编号" class="none-r" />
          <van-field label="投保附件">
            <template #input>
              <van-uploader :after-read="uploadCustomFile" :max-count="1" :max-size="2 * 1024 * 1024">
                <span style="margin-right: 20px">{{ customFileName }}</span>
                <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
              </van-uploader>
            </template>
          </van-field>
          <!-- 备注 -->
          <van-field
            v-model="form.remark"
            rows="2"
            autosize
            type="textarea"
            label="备注"
            maxlength="50"
            placeholder="备注不作为投保依据，不会展示在保单上"
            show-word-limit
            class="none-r"
          />
          <van-cell :rules="[{ required: true, message: '此项为必填项' }]">
            <template #title>
              <van-checkbox v-model="agreementHasRead" :checked-color="primaryColor">
                我已详细阅读<span class="word_color" @click="showAgreement = true">投保须知</span>的内容
              </van-checkbox>

              <van-popup
                v-model="showAgreement"
                position="bottom"
                :style="{ height: '50%', padding: '10px', BoxSizing: 'border-box' }"
                round
                closeable
              >
                <h3>投保须知</h3>
                <div v-html="product?.additional?.notice"></div>
              </van-popup>
            </template>
          </van-cell>
          <div class="btn-box">
            <van-button block :color="primaryColor" size="small" @click="onSubmit">预览投保单</van-button>
            <van-button
              block
              plain
              :color="primaryColor"
              size="small"
              @click="stagingFrom"
              v-if="$route.query.from !== 'edit'"
              >暂存投保单</van-button
            >
          </div>
        </div>
      </div>
    </van-form>
    <!-- 地区选择器 -->
    <van-popup v-model="showDeparture" position="bottom">
      <van-area
        :area-list="areaList"
        @confirm="departureAreaConfirm"
        @cancel="showDeparture = false"
        :value="form.departure_code !== undefined ? form.departure_code : ''"
        :columns-num="2"
      />
    </van-popup>
    <van-popup v-model="showTransmit" position="bottom">
      <van-area
        :area-list="areaList"
        @confirm="transmitAreaConfirm"
        @cancel="showTransmit = false"
        :value="form.transmit_code !== undefined ? form.transmit_code : ''"
        :columns-num="2"
      />
    </van-popup>
    <van-popup v-model="showDestination" position="bottom">
      <van-area
        :area-list="areaList"
        @confirm="destinationAreaConfirm"
        @cancel="showDestination = false"
        :value="form.destination_code !== undefined ? form.destination_code : ''"
        :columns-num="2"
      />
    </van-popup>
  </div>
</template>

<script>
import { getGoodsSuggestion } from '@/apis/policy'
import {
  getProductGoodsTypes,
  getProductLoadingMethods,
  getProductPackingMethods,
  getProductTransportMethods
} from '@/apis/product'
import { userKey } from '@/config'
import AddressParser from '@/utils/addressparser'
import { digitUppercase } from '@/utils/func'
import { areaList as sourceAreaList } from '@vant/area-data'
import dayjs from 'dayjs'
import { Dialog } from 'vant'
import DomesticProduct from './../components/DomesticProduct'
import TPICPorts from './../components/TpicPorts'

const PRODUCT_TYPE = 1

export default {
  components: {
    DomesticProduct,
    'tpic-ports': TPICPorts
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  filters: {
    chineseCoverage(amount) {
      return digitUppercase(amount)
    }
  },
  data() {
    return {
      showDeparture: false,
      showDestination: false,
      showTransmit: false,
      showAgreement: false,
      isExceptGoods: null,
      contactCollapse: '',
      isSame: false,
      form: {
        is_new: 1,
        type: PRODUCT_TYPE,
        product_id: -1,
        subject_id: -1,
        subject_category_ids: [],
        manual_conditions: '',
        policyholder: '',
        policyholder_type: 0,
        policyholder_overseas: 0,
        policyholder_idcard_no: '',
        policyholder_idcard_issue_date: '',
        policyholder_idcard_valid_till: '',
        insured: '',
        insured_type: 0,
        insured_overseas: 0,
        insured_idcard_no: '',
        insured_idcard_issue_date: '',
        insured_idcard_valid_till: '',
        policyholder_phone_number: '',
        insured_phone_number: '',
        policyholder_address: '',
        insured_address: '',
        sticky_note: '',
        goods_type_id: '',
        packing_method_id: '',
        loading_method_id: '',
        goods_name: '',
        goods_amount: '',
        transport_method_id: '',
        departure: '',
        departure_port: '',
        departure_addr: '',
        destination: '',
        destination_port: '',
        destination_addr: '',
        transmit: '',
        transmit_port: '',
        transmit_addr: '',
        transport_no: '',
        invoice_no: '',
        waybill_no: '',
        anti_dated_file: '',
        shipping_date: '',
        coverage: '',
        remark: '',
        custom_file: '',
        ship_construction_year: '',
        with_user_special_and_deductible: 0,
        deductible: '',
        special_agreement: '',
        execpt_goods_alert: []
      },
      product: {},
      agreementHasRead: false,

      // 客户信息
      policyholderIssueDatePicker: false,
      policyholderValidTillPicker: false,
      insuredIssueDatePicker: false,
      insuredValidTillPicker: false,

      // 倒签
      fileName: '',

      // 货物类别
      goodsType: '',
      goodsTypes: [],
      goodsTypePicker: false,
      goodsTypeDefaultIndex: 0,

      // 包装方式
      packingMethod: '',
      packingMethods: [],
      packingMethodPicker: false,
      packingMethodDefaultIndex: 0,

      // 运输方式
      transportMethod: '',
      transportMethods: [],
      transportMethodPicker: false,
      transportMethodDefaultIndex: 0,

      // 装载方式
      loadingMethod: '',
      loadingMethods: [],
      loadingMethodPicker: false,
      loadingMethodDefaultIndex: 0,

      shipConstructionYearPicker: {
        show: false,
        value: ''
      },

      // 起运日期
      shippingDatePicker: false,

      // 投保附件
      customFileName: ''
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    showAntiDatedFileUploader() {
      const antiDatedDays = this.antiDatedDays

      return antiDatedDays !== undefined && antiDatedDays > 0
    },
    shippingDatePickerMinDate() {
      const antiDatedDays = this.antiDatedDays || 0
      if (antiDatedDays > 0) {
        return dayjs(Date.now()).subtract(antiDatedDays, 'day').toDate()
      } else {
        return new Date(Date.now())
      }
    },
    clientMinDate() {
      return dayjs(Date.now()).subtract(20, 'year').toDate()
    },
    clientMaxDate() {
      return dayjs(Date.now()).add(20, 'year').toDate()
    },
    user() {
      return JSON.parse(localStorage.getItem(userKey))
    },
    areaList() {
      const disabledRegions = this.product?.additional?.disabled_regions?.split('|') || []

      Object.keys(sourceAreaList.province_list).forEach((provinceCode) => {
        if (disabledRegions.includes(sourceAreaList.province_list[provinceCode])) {
          delete sourceAreaList.province_list[provinceCode]

          provinceCode = provinceCode.replace(/0+$/, '')
          Object.keys(sourceAreaList.city_list).forEach((cityCode) => {
            if (cityCode.startsWith(provinceCode)) {
              delete sourceAreaList.city_list[cityCode]
            }
          })
        }
      })

      return sourceAreaList
    },
    antiDatedDays() {
      let date = this?.product?.additional?.anti_dated_days
      if (this?.product?.additional?.anti_date_is_in_transports == 1) {
        date = this?.product?.additional?.anti_date_transport_data.find(
          (item) => item.transport_id == this.form.transport_method_id
        )?.value
      }
      return date
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, this.form, value)

          this.agreementHasRead = true
        }
      }
    },
    isSame(value) {
      if (value) {
        this.form.insured = this.form.policyholder
        this.form.insured_phone_number = this.form.policyholder_phone_number
        this.form.insured_address = this.form.policyholder_address
      }
    },
    'form.goods_type_id': {
      handler(value) {
        this.triggerGoodsType(value)
      }
    },
    'form.transport_method_id': {
      handler(value) {
        this.triggerTransportMethod(value)
      }
    },
    'form.packing_method_id': {
      handler(value) {
        this.triggerPackingMethod(value)
      }
    },
    loadingMethods(value) {
      if (!value.some((e) => e.id === this.form.loading_method_id)) {
        this.loadingMethod = ''
        this.form.loading_method_id = ''
      }
    },
    'form.loading_method_id': {
      handler(value) {
        this.triggerLoadingMethod(value)
      }
    },
    'form.coverage'(value) {
      if (value > this.product?.additional?.coverage) {
        Dialog.alert({
          title: '保险金额超出提醒',
          message: '保险金额超限请转人工审核进行询价',
          theme: 'round-button'
        })
      }
    },
    product: {
      deep: true,
      immediate: true,
      handler(value) {
        if (value.id !== undefined) {
          if (value.id !== this.form.product_id) {
            this.form.product_id = value.id
            this.form.transport_method_id = ''
            this.form.packing_method_id = ''
            this.form.loading_method_id = ''
            this.form.goods_type_id = ''
          }

          this.fetchGoodsTypes()
          this.fetchPackingMethods()
          this.fetchTransportMethods()

          if (value.company.identifier == 'DIC') {
            // 东海需要填写电话及地址
            this.contactCollapse = '1'
          } else {
            this.contactCollapse = ''
          }
        }
      }
    },
    'form.departure_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.departure[1] !== city) {
        this.form.departure = [province, city].join('-')
      }
    },
    'form.destination_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.destination[1] !== city) {
        this.form.destination = [province, city].join('-')
      }
    },
    'form.transmit_addr'(value, oldValue) {
      if (!oldValue && this.$route.params.from !== undefined) {
        return
      }

      const { province, city } = AddressParser.parse(value)
      if (city && this.form.transmit[1] !== city) {
        this.form.transmit = [province, city].join('-')
      }
    },
    'form.policyholder_type'(value) {
      if (value === 1 && this.user.platform_id === 2) {
        this.form.policyholder_phone_number = '111111'
        this.form.policyholder_address = '代理'
        this.form.policyholder_idcard_issue_date = '2020-09-01'
        this.form.policyholder_idcard_valid_till = '2030-09-01'
      }
    },
    'form.insured_type'(value) {
      if (value === 1 && this.user.platform_id === 2) {
        this.form.insured_phone_number = '111111'
        this.form.insured_address = '代理'
        this.form.insured_idcard_issue_date = '2020-09-01'
        this.form.insured_idcard_valid_till = '2030-09-01'
      }
    },
    'form.shipping_date': {
      handler(value) {
        this.form.shipping_date = dayjs(value).format('YYYY-MM-DD')
      }
    }
  },
  created() {
    if (this.$route.query.from === undefined) {
      this.form.policyholder = this.user.name
      this.form.shipping_date = dayjs(Date.now()).format('YYYY-MM-DD')
    }
  },
  methods: {
    triggerGoodsType(value) {
      const goodsTypes = this.goodsTypes

      if (value && goodsTypes.length > 0) {
        const idx = goodsTypes.findIndex((e) => e.id === value)

        this.goodsType = goodsTypes[idx]?.display_name
        this.goodsTypeDefaultIndex = idx
      }
    },
    triggerTransportMethod(value) {
      const transportMethods = this.transportMethods

      if (value && transportMethods.length > 0) {
        const idx = transportMethods.findIndex((e) => e.id === value)

        this.transportMethod = transportMethods[idx]?.display_name
        this.transportMethodDefaultIndex = idx
        this.loadingMethods = (transportMethods[idx]?.loading_methods || []).map((e) => {
          e.text = e.display_name

          return e
        })

        this.triggerLoadingMethod(this.form.loading_method_id)
      }
    },
    triggerPackingMethod(value) {
      const packingMethods = this.packingMethods

      if (value && packingMethods.length > 0) {
        const idx = packingMethods.findIndex((e) => e.id === value)

        this.packingMethod = packingMethods[idx]?.display_name
        this.packingMethodDefaultIndex = idx
      }
    },
    triggerLoadingMethod(value) {
      const loadingMethods = this.loadingMethods

      if (value && loadingMethods.length > 0) {
        const idx = loadingMethods.findIndex((e) => e.id === value)

        this.loadingMethod = loadingMethods[idx]?.display_name
        this.loadingMethodDefaultIndex = idx
      }
    },
    fetchGoodsTypes() {
      getProductGoodsTypes(this.product.id).then((r) => {
        const goodsTypes = []
        r.data.data.forEach((type) => {
          type.text = type.display_name

          goodsTypes.push(type)
        })

        this.goodsTypes = goodsTypes

        this.triggerGoodsType(this.form.goods_type_id)
      })
    },
    fetchPackingMethods() {
      getProductPackingMethods(this.product.id).then((r) => {
        const methods = []
        r.data.data.forEach((type) => {
          type.text = type.display_name

          methods.push(type)
        })

        this.packingMethods = methods

        this.triggerPackingMethod(this.form.packing_method_id)
      })
    },
    fetchTransportMethods() {
      getProductTransportMethods(this.product.id).then((r) => {
        const methods = []
        r.data.data.forEach((type) => {
          type.text = type.display_name

          methods.push(type)
        })

        this.transportMethods = methods
        if (this.$route.query.from === undefined) {
          const method = methods.find((m) => m.display_name === '公路运输')
          if (method) {
            this.form.transport_method_id = method.id
          }
        }

        this.triggerTransportMethod(this.form.transport_method_id)
      })
    },
    fetchLoadingMethods() {
      getProductLoadingMethods(this.product.id).then((r) => {
        const methods = []
        r.data.data.forEach((type) => {
          type.text = type.display_name

          methods.push(type)
        })

        this.loadingMethods = methods

        this.triggerLoadingMethod(this.form.loading_method_id)
      })
    },
    // 地区选择
    departureAreaConfirm(values) {
      this.form.departure = values
        .filter((item) => !!item)
        .map((item) => item.name)
        .join('-')

      this.showDeparture = false
    },
    transmitAreaConfirm(values) {
      this.form.transmit = values
        .filter((item) => !!item)
        .map((item) => item.name)
        .join('-')

      this.showTransmit = false
    },
    destinationAreaConfirm(values) {
      this.form.destination = values
        .filter((item) => !!item)
        .map((item) => item.name)
        .join('-')

      this.showDestination = false
    },
    // 暂存投保单
    stagingFrom() {
      if (!this.agreementHasRead) {
        this.$toast('请阅读投保须知和保外事项')
        return
      }

      const _temp = Object.assign({}, this.form)
      _temp.departure = (_temp.departure + ':' + _temp?.departure_addr || '').replace(/:(\s+)?$/, '')
      if (_temp.transmit) {
        _temp.transmit = (_temp.transmit + ':' + _temp?.transmit_addr || '').replace(/:(\s+)?$/, '')
        delete _temp.transmit_addr
      }
      _temp.destination = (_temp.destination + ':' + _temp?.destination_addr || '').replace(/:(\s+)?$/, '')
      _temp.shipping_date = dayjs(_temp.shipping_date).isValid()
        ? dayjs(_temp.shipping_date).format('YYYY-MM-DD')
        : dayjs().format('YYYY-MM-DD')

      delete _temp.departure_addr
      delete _temp.destination_addr

      this.$emit('on-draft', _temp, this.product)
    },
    async onSubmit() {
      if (this.isExceptGoods === null) {
        this.$toast('请确认是否为除外标的中的货物')
        return
      }

      if (this.isExceptGoods) {
        this.$toast('请选择正确的标的投保或联系业务人员')
        return
      }

      if (!this.agreementHasRead) {
        this.$toast('请阅读投保须知和保外事项')
        return
      }

      if (this.form?.coverage > this.product?.additional?.coverage) {
        return Dialog.alert({
          title: '保险金额超出提醒',
          message: '保险金额超限请转人工审核进行询价',
          theme: 'round-button'
        })
      }

      if (
        this.form?.coverage >= 1000000 &&
        this.form.transport_method_id == 2 &&
        this.product?.company?.identifier === 'PICC'
      ) {
        if (this.form.ship_construction_year == '') {
          this.$toast('请填写船舶建造年')
          return
        }
      }

      if (
        new TextEncoder().encode(`${this.form.policyholder_address}${this.form.policyholder_phone_number}`).length > 150
      ) {
        return Dialog.alert({
          title: '投保人信息超出提醒',
          message: '投保人地址+电话不能超过150个字符',
          theme: 'round-button'
        })
      }

      if (new TextEncoder().encode(`${this.form.insured_address}${this.form.insured_phone_number}`).length > 150) {
        return Dialog.alert({
          title: '被保人信息超出提醒',
          message: '被保人地址+电话不能超过150个字符',
          theme: 'round-button'
        })
      }

      const _temp = Object.assign({}, this.form)
      _temp.departure = (_temp.departure + ':' + _temp?.departure_addr || '').replace(/:(\s+)?$/, '')
      _temp.transmit = (_temp.transmit + ':' + _temp?.transmit_addr || '').replace(/:(\s+)?$/, '')
      _temp.destination = (_temp.destination + ':' + _temp?.destination_addr || '').replace(/:(\s+)?$/, '')
      _temp.shipping_date = dayjs(_temp.shipping_date).format('YYYY-MM-DD')

      delete _temp.departure_addr
      delete _temp.transmit_addr
      delete _temp.destination_addr

      // 人工审核不验证
      if (this.form.subject_id === 3) {
        return this.$emit('on-preview', _temp, this.product)
      }

      const { data } = await getGoodsSuggestion({
        subject_id: this.form.subject_id,
        company_branch_id: this.product.company.branch.id,
        product_type: PRODUCT_TYPE,
        keyword: this.form.goods_name
      })

      if (data.data.condition.matched) {
        const message =
          data.data.condition.action === 1
            ? `根据您所填写货物名称，系统判定此货物<span class="text-danger">${data.data.condition.keyword}</span>需要附加免赔和特别约定，请确认是否附加？若不附加，请选择<span class="text-danger">人工审核</span>投保。`
            : `根据您所填写货物名称，系统判定此货物<span class="text-danger">${data.data.condition.keyword}</span>需要转人工审核投保，请确认是否转人工审核？`
        Dialog.confirm({
          title: '投保提示',
          message,
          theme: 'round-button',
          showCancelButton: true,
          confirmButtonText: '是',
          cancelButtonText: '否',
          beforeClose: (action, done) => {
            if (action === 'confirm') {
              _temp.with_user_special_and_deductible = 1
              // 转人工
              if (data.data.condition.action === 2) {
                _temp.deductible = ''
                _temp.special_agreement = ''

                this.form.subject_id = 3
                this.form.product_id = ''
                done()
              } else {
                _temp.deductible = data.data.condition.deductible
                _temp.special_agreement = data.data.condition.special_agreement
                done()
                this.nextValidateGoodsName(data, _temp)
              }
            } else {
              _temp.with_user_special_and_deductible = 0
              _temp.deductible = ''
              _temp.special_agreement = ''
              done()
            }
          }
        })
      } else {
        _temp.with_user_special_and_deductible = 0
        _temp.deductible = ''
        _temp.special_agreement = ''
        this.nextValidateGoodsName(data, _temp)
      }
    },
    nextValidateGoodsName(data, _temp) {
      if (data.data.subject.subject) {
        const description =
          data.data.subject.subject == '人工审核'
            ? `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">人工审核货物</span>，请再次确认您的货物是否为<span class="text-danger">人工审核货物</span>？如果为<span class="text-danger">人工审核货物</span>，请转至<span class="text-danger">人工审核</span>端口投保或联系业务人员咨询。`
            : `根据您所填写货物名称，系统判断此货物可能为<span class="text-danger">${data.data.subject.subject}</span>，请再次确认您的货物是否为<span class="text-danger">${data.data.subject.subject}</span>？如果为<span class="text-danger">${data.data.subject.subject}</span>，请转至<span class="text-danger">${data.data.subject.subject}</span>端口投保或联系业务人员咨询。`
        Dialog.confirm({
          title: '投保提示',
          message: description,
          theme: 'round-button',
          showCancelButton: true,
          confirmButtonText: '是',
          cancelButtonText: '否',
          beforeClose: (action, done) => {
            if (action === 'confirm') {
              Dialog.alert({
                title: '投保提示',
                message: `请正确选择货物类别投保或联系业务人员。`,
                theme: 'round-button'
              })
              _temp.execpt_goods_alert = []
              done()
            } else {
              done()
              _temp.execpt_goods_alert = [data.data.subject.subject, description]
              setTimeout(() => this.nextConfirm(_temp), 100)
            }
          }
        })
      } else {
        setTimeout(() => this.nextConfirm(_temp), 100)
      }
    },
    nextConfirm(_temp) {
      if (
        this?.product?.channel?.platform?.includes(this.user.platform_id) &&
        this?.product?.additional?.inform?.length > 1
      ) {
        Dialog.alert({
          title: '投保告知',
          message: this?.product?.additional?.inform,
          messageAlign: 'left',
          allowHtml: true,
          theme: 'round-button'
        }).then(() => {
          this.$emit('on-preview', _temp, this.product)
        })
      } else {
        this.$emit('on-preview', _temp, this.product)
      }
    },
    // 倒签
    uploadAntiDatedFile(file) {
      // 此时可以自行将文件上传至服务器
      this.fileName = file.file.name
      this.form.anti_dated_file = file.file
    },
    uploadCustomFile(file) {
      this.customFileName = file.file.name
      this.form.custom_file = file.file
    },
    confirmGoodsType(value) {
      this.goodsTypePicker = false
      if (value) {
        this.goodsType = value.text
        this.form.goods_type_id = value.id
      }
    },
    confirmPickingMethod(value) {
      this.packingMethodPicker = false
      if (value) {
        this.packingMethod = value.text
        this.form.packing_method_id = value.id
      }
    },
    confirmTransportMethod(value) {
      this.transportMethodPicker = false
      if (value) {
        this.transportMethod = value.text
        this.form.transport_method_id = value.id
      }
    },
    confirmLoadingMethod(value) {
      this.loadingMethodPicker = false
      if (value) {
        this.loadingMethod = value.text
        this.form.loading_method_id = value.id
      }
    },
    confirmShipConstructionYear(date) {
      this.shipConstructionYearPicker.show = false
      this.form.ship_construction_year = dayjs(date).format('YYYY')
    },
    confirmShippingDate(date) {
      this.shippingDatePicker = false
      this.form.shipping_date = dayjs(date).format('YYYY-MM-DD')
    },
    confirmPolicyholderIssueDate(date) {
      this.policyholderIssueDatePicker = false
      this.form.policyholder_idcard_issue_date = dayjs(date).format('YYYY-MM-DD')
    },
    confirmPolicyholderValidTill(date) {
      this.policyholderValidTillPicker = false
      this.form.policyholder_idcard_valid_till = dayjs(date).format('YYYY-MM-DD')
    },
    confirmInsuredIssueDate(date) {
      this.insuredIssueDatePicker = false
      this.form.insured_idcard_issue_date = dayjs(date).format('YYYY-MM-DD')
    },
    confirmInsuredValidTill(date) {
      this.insuredValidTillPicker = false
      this.form.insured_idcard_valid_till = dayjs(date).format('YYYY-MM-DD')
    }
  }
}
</script>

<style lang="less" scoped>
.gzhyx {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
  }
}
// 必填项
.required::after {
  content: '*' !important;
  color: red;
  left: 3.5rem;
  font-size: 18px;
  bottom: auto;
  transform: none;
}

/deep/ .none-r {
  .van-cell__title.van-field__label {
    span {
      &::after {
        content: none !important;
      }
    }
  }
}
// 保单填写
.van-cell {
  img {
    width: 12px;
    // height: 15px;
    background-size: contain;
    line-height: 15px;
  }
  .word {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-left: 5px;
  }
}
// 左侧内容label宽度
/deep/ .van-cell__title.van-field__label {
  width: 120px;
}
// 上传文件位置
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-end;
}
.word_color {
  color: var(--primary-color);
}
.van-checkbox {
  font-size: 14px;
  color: #333;
}
.btn-box {
  padding: 10px 15px;
  .van-button {
    margin-bottom: 10px;
  }
}
/deep/ .van-cell--borderless::after,
.van-cell:last-child::after {
  display: block;
  border-bottom: none;
}
</style>
